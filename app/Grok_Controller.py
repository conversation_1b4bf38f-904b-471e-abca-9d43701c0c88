import os
from typing import Any

import openai
from langchain.adapters import openai
from langchain_core.documents import Document
from langchain_core.messages import ToolMessage, HumanMessage, SystemMessage
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_openai import OpenAIEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_xai import Chat<PERSON><PERSON><PERSON>

from app import MyTools, Utils
from app.model import PromptData, AnalysisRunRequest

XAI_API_KEY = os.environ.get("XAI_API_KEY", "<--XAI_API_KEY-->")
print(f"Using XAI with API key: ...{XAI_API_KEY[-8:]}")

DEFAULT_GROK_MODEL = "grok-3-latest"
DEFAULT_TEMPERATURE = 0.01


def _add_role(role: str, message: str, message_list: list[Any]) -> list[Any]:
    if role == 'system_prompt':
        message_list.append({"role": "system", "content": message})
    elif role == 'user_prompt':
        message_list.append({"role": "user", "content": message})
    else:
        raise Exception(f"Invalid role: {role}")

    return message_list


def _add_ticker_info(inputs, prompt: PromptData):
    """Add ticker and ticker set information to the inputs."""

    if 'ticker' in prompt:
        inputs.append({"role": "user", "content": f"For the following ticker: {prompt['ticker']}"})

    if 'tickers' in prompt:
        inputs.append({"role": "user", "content": f"For the following tickers: {prompt['tickers']}"})

    # if 'ticker_set' in prompt:
    #     inputs.append({"role": "user", "content": f"For the following ticker set: {prompt['ticker_set']}"})

    return inputs


def run_completion_api(prompt, step_data, documents: list[Any]):
    context = Utils.documents_to_context(documents)

    messages = []
    if 'system_prompt' in step_data:
        _add_role('system_prompt', step_data['system_prompt'], messages)

    # Add ticker information if analysis is provided
    ticker_inputs = []
    ticker_inputs = _add_ticker_info(ticker_inputs, prompt)
    ticker_info = ""
    if ticker_inputs:
        ticker_info = "\n".join([input['content'] for input in ticker_inputs]) + "\n"

    user_prompt = step_data['user_prompt'] if "user_prompt" in step_data else ""
    _add_role("user_prompt",
              f"{ticker_info}Prompt: {user_prompt}\ncontext: {context}\n", messages)

    model = prompt['model'].replace("grok:", "") if "model" in prompt else DEFAULT_GROK_MODEL
    temperature = prompt['temperature'] if "temperature" in prompt else DEFAULT_TEMPERATURE

    response = openai.chat.completions.create(
        messages=messages,
        seed=prompt['seed'] if "seed" in prompt else 42,
        model=model,
        # store=prompt['store'] if "store" in prompt else False,
        timeout=prompt['timeout'] if "timeout" in prompt else None,
        stop=prompt['stop'] if "stop" in prompt else None,
        temperature=temperature,
        # top_p=prompt['top_p'] if "top_p" in prompt else 0,
        # response_format={'type': prompt['response_format']} if "response_format" in prompt else None,
        # max_completion_tokens=10000,
    )
    return response.choices[0].message.content, response


def _load_doc(doc) -> InMemoryVectorStore:
    embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
    vector_store = InMemoryVectorStore(embeddings)
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    all_splits = text_splitter.split_documents(doc['text'])
    vector_store.add_documents(documents=all_splits)
    return vector_store


def run_langchain(request: AnalysisRunRequest, step_data, documents: list[Any]):
    from langchain_xai import ChatXAI
    model = request.prompt.model.replace("grok:", "") or DEFAULT_GROK_MODEL
    llm = ChatXAI(
        api_key=XAI_API_KEY,
        model=model,
        temperature=DEFAULT_TEMPERATURE)

    messages = []
    if 'system_prompt' in step_data:
        messages.append(SystemMessage(step_data.system_prompt))

    user_prompt = step_data.user_prompt or f"List all documents for {request.tickers}"
    messages.append(HumanMessage(user_prompt))

    vec_store: InMemoryVectorStore = None
    if request.document_contents:
        if request.prompt.vectorize:
            vec_store = InMemoryVectorStore(OpenAIEmbeddings(model="text-embedding-ada-002"))
            docs = []
            for doc_content in request.document_contents:
                docs.append(Document(page_content=doc_content.content, metadata={"source": "ESPM"}))

            vec_store.add_documents(docs)

        else:
            for doc_content in request.document_contents:
                messages.append(HumanMessage(f"Context: {doc_content.document_ref.type}\n{doc_content.content}"))

    final_llm = llm
    if step_data.tools:
        tools = [MyTools.get_10k_text, MyTools.list_documents]
        tool_mapping = {"download_10k": MyTools.get_10k_text, "list_documents": MyTools.list_documents}

        final_llm = llm.bind_tools(tools)
        ai_msg = final_llm.invoke(messages)

        print(ai_msg)

        for tool_call in ai_msg.tool_calls:
            selected_tool = tool_mapping[tool_call['name'].lower()]
            tool_output = selected_tool.invoke(tool_call['args'])
            messages.append(ToolMessage(tool_output, tool_call_id=tool_call["id"]))

    ai_msg = final_llm.invoke(messages)

    print(ai_msg)

    reply = ai_msg.content
    return reply, ai_msg


def summarize(text: str, language: str = "en", max_tokens: int = 2000) -> str:
    llm = ChatXAI(
        api_key=XAI_API_KEY,
        model=DEFAULT_GROK_MODEL,
        temperature=DEFAULT_TEMPERATURE)

    response = llm.invoke(
        model=DEFAULT_GROK_MODEL,
        input=[
            SystemMessage("You are a helpful assistant that summarizes text."
                          " If text is already in the target language, do not translate. "
                          " Do not include any disclaimers. Do not include any reasoning."),
            HumanMessage(f"Summarize the following text: {text}"),
            HumanMessage(f"Translate the result to {language}")],
        temperature=DEFAULT_TEMPERATURE,
        max_tokens=max_tokens
    )
    return response.content


def translate(text: str, language: str = "en"):
    llm = ChatXAI(
        api_key=XAI_API_KEY,
        model=DEFAULT_GROK_MODEL,
        temperature=DEFAULT_TEMPERATURE)

    response = llm.invoke(
        model=DEFAULT_GROK_MODEL,
        input=[
            SystemMessage("You are a helpful assistant that translates text."
                          "If text is already in the target language, do not translate, just reprint the text."
                          " Do not include any disclaimers. Do not include any reasoning."),
            HumanMessage(f"Translate the following text to {language}: {text}")],
        temperature=DEFAULT_TEMPERATURE,
    )
    return response.content
