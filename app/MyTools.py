from langchain_core.tools import tool

from app.SEC_Controller import SEC_Controller

sec_controller = SEC_Controller()

@tool
def get_10k_text(ticker: str, year: int) -> str:
    """
        Download 10-K file for a ticker and year
        Args:
            ticker (str): The ticker symbol of the company.
            year (int): The year to retrieve the 10-K for.
        Returns:
            str: The text of the 10-K document.
    """
    text = sec_controller.get_10k(ticker, year).text
    return text

@tool
def list_documents(tickers: list[str] = None) -> str:
    """
        Get a list of all SEC filings for a list of tickers.
        Args:
            tickers (list[str]): A list of tickers to retrieve filings for.
        Returns:
            str: A list of all SEC filings for the specified tickers.
    """
    ret = ""
    for ticker in tickers:
        cik = sec_controller.symbol_store.get_cik(ticker)
        filings = sec_controller.get_company_filings(cik)

        for f in filings:
            ret += f"ticker:{ticker} form: {f.form} filing_date: {f.filing_date} report_date: {f.report_date} accession_number: {f.accession_number}\n"

    return ret
