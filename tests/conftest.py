"""
Pytest configuration file for the test suite.
"""
import os
import sys
from datetime import datetime
from unittest.mock import Mock, MagicMock

import pytest

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

@pytest.fixture(scope="session")
def test_app():
    """Create a test instance of the FastAPI app."""
    from app.app import app
    return app

@pytest.fixture
def mock_sec_controller():
    """Create a mock SEC controller for testing."""
    mock_sec = Mock()

    # Set up common mock responses
    mock_sec.symbol_store.get_symbol_cik.return_value = 320193
    mock_sec.symbol_store.get_symbol_ticker.return_value = "AAPL"
    mock_sec.symbol_store.get_symbol_name.return_value = "Apple Inc."
    mock_sec.symbol_store.get_cik.return_value = 320193

    mock_sec.get_company_submissions.return_value = {
        "cik": 320193,
        "entityType": "operating",
        "sic": "3571",
        "sicDescription": "Electronic Computers"
    }

    # Mock filing object with proper attributes
    mock_filing = Mock()
    mock_filing.form = "10-K"
    mock_filing.accession_number = "0000320193-23-000064"
    mock_filing.filing_date = "2023-11-03"
    mock_filing.report_date = "2023-09-30"

    mock_sec.get_company_filings.return_value = [mock_filing]

    return mock_sec

@pytest.fixture
def mock_analysis_store():
    """Create a mock analysis results store for testing."""
    mock_store = Mock()

    # Mock analysis result
    from app.model import AnalysisResult, PromptData
    mock_result = AnalysisResult(
        analysis_id="test-analysis-123",
        status="completed",
        prompt=PromptData(name="Test Analysis", model="grok:grok-3-latest"),
        tickers=["AAPL"],
        started_at=datetime.now(),
        completed_at=datetime.now()
    )

    mock_store.create.return_value = "test-analysis-123"
    mock_store.get_result.return_value = mock_result
    mock_store.update.return_value = None
    mock_store.get_analysis_ids.return_value = ["test-analysis-123", "test-analysis-456"]

    return mock_store

@pytest.fixture
def mock_document_store():
    """Create a mock document store for testing."""
    mock_store = Mock()

    from app.model import DocumentContent, DocumentType
    mock_document = DocumentContent(
        type=DocumentType.TEN_K,
        cik=320193,
        ticker="AAPL",
        filing_date="2023-11-03",
        report_date="2023-09-30",
        title="Apple Inc. 10-K",
        content="Sample 10-K content for testing"
    )

    mock_store.get_documents.return_value = [mock_document]

    return mock_store

@pytest.fixture
def sample_analysis_request():
    """Create a sample analysis request for testing."""
    from app.model import AnalysisRunRequest, PromptData, PromptStepData, DocumentType

    return AnalysisRunRequest(
        prompt=PromptData(
            name="Test Analysis",
            model="grok:grok-3-latest",
            steps=[
                PromptStepData(
                    system_prompt="You are a financial analyst",
                    user_prompt="Analyze the revenue data",
                    model_action="completion",
                    response_format="text"
                )
            ],
            document_types=[DocumentType.TEN_K]
        ),
        tickers=["AAPL", "MSFT"]
    )

@pytest.fixture
def sample_upload_request():
    """Create a sample upload request for testing."""
    from app.model import UploadFileRequest, DocumentType

    return UploadFileRequest(
        model="openai:gpt-4",
        file="Sample document content",
        document_type=DocumentType.TEN_K,
        ticker="AAPL",
        filing_date="2023-12-31",
        report_date="2023-12-31"
    )

@pytest.fixture
def sample_prompt_data():
    """Create sample prompt data for testing."""
    from app.model import PromptData, PromptStepData, DocumentType

    return PromptData(
        name="Test Prompt",
        model="grok:grok-3-latest",
        steps=[
            PromptStepData(
                system_prompt="You are a helpful assistant",
                user_prompt="Analyze this data",
                model_action="completion",
                response_format="text"
            )
        ],
        document_types=[DocumentType.TEN_K]
    )

@pytest.fixture
def sample_document_ref():
    """Create sample document reference for testing."""
    from app.model import DocumentRef, DocumentType

    return DocumentRef(
        type=DocumentType.TEN_K,
        cik=320193,
        ticker="AAPL",
        accession_number="0000320193-23-000064"
    )
