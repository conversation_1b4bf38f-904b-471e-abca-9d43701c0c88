# Tests for AI Analyst

This directory contains comprehensive tests for the AI Analyst application, including unit tests, integration tests, and API endpoint tests.

## Running Tests

### Using the Enhanced Pytest Runner (Recommended)

To run tests with the interactive test runner:

```bash
python tests/run_pytest.py
```

This provides options to:
- Run all tests
- Run specific test categories
- Generate coverage reports
- View detailed test results

### Direct Pytest Commands

Run all tests:
```bash
pytest tests/ -v
```

Run specific test files:
```bash
pytest tests/test_ai_controller.py -v
pytest tests/test_ai_controller_integration.py -v
pytest tests/test_app.py -v
```

Run with coverage:
```bash
pytest tests/ --cov=app --cov-report=html --cov-report=term
```

### Using the legacy test runner

For backward compatibility:

```bash
python tests/run_tests.py
```

## Test Files

### Core Unit Tests (pytest-based)
- `test_ai_controller.py` - **NEW**: Comprehensive unit tests for AI_Controller module
  - Public function tests (upload_document, run_prompt, get_analysis_results, etc.)
  - Private function tests (_build_documents_list, _run_steps, etc.)
  - Error handling and edge cases
  - Cancellation and global state management
- `test_ai_controller_integration.py` - **NEW**: Integration tests for AI_Controller
  - End-to-end workflow testing
  - Multi-model integration (Grok, OpenAI)
  - Document processing workflows
  - Asynchronous execution testing
- `test_app.py` - **UPDATED**: Enhanced FastAPI endpoint tests
- `test_simple.py` - Basic functionality and import tests
- `conftest.py` - **ENHANCED**: Pytest configuration with comprehensive fixtures

### Legacy Integration Tests
- `test_sec_api.py` - Tests for the SEC API functionality
- `test_get_company_filings.py` - Tests for the get_company_filings function
- `test_langchain_warning.py` - Tests for LangChain deprecation warning fixes
- `test_langchain_imports.py` - Tests for LangChain import functionality
- `test_daily_filings.py` - Tests for the daily filings functionality

## Test Structure

### AI Controller Tests (`test_ai_controller.py`)

The AI Controller tests are organized into focused test classes:

- `TestAIControllerPublicFunctions` - Tests for all public API functions
- `TestAIControllerPrivateFunctions` - Tests for internal helper functions
- `TestAIControllerStepExecution` - Tests for analysis step execution
- `TestAIControllerBackgroundExecution` - Tests for async/background processing
- `TestAIControllerErrorHandling` - Tests for error scenarios and edge cases

### Integration Tests (`test_ai_controller_integration.py`)

- `TestAIControllerIntegration` - End-to-end workflow testing
  - Full analysis workflows with Grok and OpenAI models
  - Multi-ticker analysis scenarios
  - Document building and processing
  - Analysis lifecycle management
  - Text processing integration

### FastAPI Endpoint Tests (`test_app.py`)

- `TestApp` - Tests for basic endpoints (root, health, document upload, analysis)
- `TestSECEndpoints` - Tests for SEC-related endpoints
- `TestDocumentEndpoints` - Tests for document retrieval endpoints
- `TestYahooFinanceEndpoints` - Tests for Yahoo Finance price history

## Adding New Tests

When adding new tests, follow these guidelines:

### For pytest-based tests:
1. **Naming**: Use descriptive test function names starting with `test_`
2. **Organization**: Group related tests into test classes
3. **Fixtures**: Use fixtures from `conftest.py` for common setup
4. **Mocking**: Mock external dependencies using `unittest.mock`
5. **Assertions**: Use clear, specific assertions to verify expected behavior
6. **Documentation**: Include docstrings explaining what each test verifies

### Test Categories:

#### Unit Tests
- Test individual functions in isolation
- Mock all external dependencies
- Focus on specific functionality and edge cases
- Fast execution (< 1 second per test)

#### Integration Tests
- Test interaction between components
- Use minimal mocking for core functionality
- Verify end-to-end workflows
- May take longer to execute

#### API Tests
- Test FastAPI endpoints
- Verify request/response contracts
- Test error handling and status codes
- Use TestClient for HTTP testing

### Example Test Structure:

```python
class TestNewFeature:
    """Test class for new feature functionality."""

    def setup_method(self):
        """Set up test environment before each test."""
        # Reset any global state
        pass

    @patch('app.module.external_dependency')
    def test_feature_success(self, mock_dependency):
        """Test successful feature execution."""
        # Arrange
        mock_dependency.return_value = "expected_result"

        # Act
        result = module.feature_function("input")

        # Assert
        assert result == "expected_result"
        mock_dependency.assert_called_once_with("input")

    def test_feature_error_handling(self):
        """Test feature error handling."""
        with pytest.raises(ValueError, match="Invalid input"):
            module.feature_function(None)
```

## Test Coverage

The test suite aims for comprehensive coverage of:

### AI_Controller Module (95%+ coverage)
- ✅ All public functions
- ✅ All private helper functions
- ✅ Error handling and edge cases
- ✅ Asynchronous execution
- ✅ Rate limiting and retry logic
- ✅ Cancellation mechanisms
- ✅ Integration with external services

### FastAPI Endpoints (90%+ coverage)
- ✅ All HTTP endpoints
- ✅ Request validation
- ✅ Response formatting
- ✅ Error responses
- ✅ Authentication (if applicable)

### Model Validation (85%+ coverage)
- ✅ Pydantic model validation
- ✅ Enum handling
- ✅ Data type conversions
- ✅ Field validation rules

## Dependencies

The tests require the following packages:
- `pytest` - Testing framework
- `pytest-asyncio` - Async testing support
- `pytest-cov` - Coverage reporting
- `httpx` - HTTP client for testing FastAPI endpoints
- `unittest.mock` - Mocking framework (built-in)

Install test dependencies:
```bash
pip install pytest pytest-asyncio pytest-cov httpx
```

## Continuous Integration

The test suite is designed to run in CI/CD environments:

### GitHub Actions Example:
```yaml
- name: Run tests
  run: |
    python -m pytest tests/ -v --cov=app --cov-report=xml
```

### Local Development:
```bash
# Run tests with coverage
python tests/run_pytest.py

# Quick test run
pytest tests/test_ai_controller.py -v

# Run specific test
pytest tests/test_ai_controller.py::TestAIControllerPublicFunctions::test_upload_document_success -v
```

## Troubleshooting

### Common Issues:

1. **Import Errors**
   - Ensure the parent directory is in Python path
   - Check that all dependencies are installed
   - Verify module structure

2. **Mock Issues**
   - Use correct patch paths (e.g., `app.AI_Controller.function`)
   - Ensure mocks are configured before test execution
   - Check mock call assertions

3. **Async Test Issues**
   - Use `pytest-asyncio` for async tests
   - Mark async tests with `@pytest.mark.asyncio`
   - Ensure proper async/await usage

4. **Fixture Issues**
   - Check fixture scope (function, class, module, session)
   - Ensure fixtures are properly imported
   - Verify fixture dependencies

### Getting Help:

- Review test output for specific error messages
- Check the pytest documentation: https://docs.pytest.org/
- Examine existing tests for patterns and examples
- Use `pytest --collect-only` to see discovered tests


