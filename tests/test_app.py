"""
Comprehensive unit tests for the FastAPI app endpoints.
"""
import os
import sys
from unittest.mock import patch, MagicMock

import pytest
from fastapi.testclient import TestClient

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.app import app


class TestApp:
    """Test class for FastAPI app endpoints."""

    def setup_method(self):
        """Set up test client and mocks before each test."""
        self.client = TestClient(app)

    def test_root_endpoint(self):
        """Test the root endpoint."""
        response = self.client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "Python FastAPi OK"

    def test_health_check_endpoint(self):
        """Test the health check endpoint."""
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "Python FastAPi Healthy"

    @patch('app.AI_Controller.upload_document')
    def test_upload_document_success(self, mock_upload):
        """Test successful document upload."""
        # Mock the upload_document function
        mock_upload.return_value = {
            "status": "OK",
            "message": "Document uploaded successfully"
        }

        request_data = {
            "model": "openai:gpt-4",
            "file": "Sample document content",
            "document_type": "10-k",
            "ticker": "AAPL",
            "filing_date": "2023-12-31",
            "report_date": "2023-12-31"
        }

        response = self.client.post("/document/upload", json=request_data)
        assert response.status_code == 200
        mock_upload.assert_called_once()

    @patch('app.AI_Controller.run_prompt')
    def test_run_analysis_success(self, mock_run_prompt):
        """Test successful analysis run."""
        from app.model import AnalysisRunResponse, AnalysisRunRequest, PromptData

        # Create a proper request object
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL", "MSFT"]
        )

        # Mock the run_prompt function to return a proper AnalysisRunResponse
        mock_run_prompt.return_value = AnalysisRunResponse(
            message="Analysis started",
            request=request,
            analysis_id="test-analysis-123"
        )

        request_data = {
            "prompt": {
                "name": "Test Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["AAPL", "MSFT"]
        }

        response = self.client.post("/analysis/run", json=request_data)
        assert response.status_code == 200
        mock_run_prompt.assert_called_once()

    @patch('app.AI_Controller.run_prompt_sync')
    def test_run_analysis_sync_success(self, mock_run_prompt_sync):
        """Test successful synchronous analysis run."""
        from app.model import AnalysisRunRequest, AnalysisResultResponse, AnalysisResult, PromptData, PromptStepData
        from datetime import datetime

        request = AnalysisRunRequest(
            prompt=PromptData(name="Test Sync Analysis", model="grok:grok-3-latest",
                              steps=[PromptStepData(
                                  system_prompt="You are a financial analyst",
                                  user_prompt="Analyze the revenue data",
                                  model_action="completion",
                                  response_format="text")]),
            tickers=["AAPL"]
        )

        # Mock the run_prompt_sync function to return a proper AnalysisResult
        mock_analysis_result = AnalysisResult(
            analysis_id="test-analysis-123",
            status="completed",
            prompt=PromptData(name="Test Sync Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL"],
            results=None,  # Use None instead of strings to avoid validation errors
            started_at=datetime.now(),
            completed_at=datetime.now()
        )

        mock_run_prompt_sync.return_value = AnalysisResultResponse(
            request=request,
            result=mock_analysis_result,
            message="Analysis completed",
            status="OK"
        )

        request_data = {
            "prompt": {
                "name": "Test Sync Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["AAPL"]
        }

        response = self.client.post("/analysis/run_sync", json=request_data)
        assert response.status_code == 200
        mock_run_prompt_sync.assert_called_once()

    @patch('app.AI_Controller.get_analysis_results')
    def test_get_analysis_results_success(self, mock_get_results):
        """Test successful retrieval of analysis results."""
        from app.model import AnalysisRunRequest,AnalysisResultResponse, AnalysisResult, PromptData, PromptStepData
        from datetime import datetime

        request = AnalysisRunRequest(
            prompt=PromptData(name="Test Sync Analysis", model="grok:grok-3-latest",
                              steps=[PromptStepData(
                                  system_prompt="You are a financial analyst",
                                  user_prompt="Analyze the revenue data",
                                  model_action="completion",
                                  response_format="text")]),
            tickers=["AAPL"],
        )

        # Mock the get_analysis_results function to return a proper AnalysisResultResponse
        mock_analysis_result = AnalysisResult(
            analysis_id="test-analysis-123",
            status="completed",
            prompt=PromptData(name="Test Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL"],
            results=None,  # Use None instead of strings to avoid validation errors
            started_at=datetime.now(),
            completed_at=datetime.now()
        )

        mock_get_results.return_value = AnalysisResultResponse(
            request=request,
            status="OK",
            message="Analysis results retrieved",
            result=mock_analysis_result
        )

        response = self.client.get("/analysis/result/test-analysis-123")
        assert response.status_code == 200
        mock_get_results.assert_called_once_with("test-analysis-123")

    @patch('app.AI_Controller.get_analysis_results')
    def test_get_analysis_results_not_found(self, mock_get_results):
        """Test analysis results not found."""
        # Mock the function to raise an exception
        mock_get_results.side_effect = Exception("Analysis not found")

        response = self.client.get("/analysis/result/nonexistent-id")
        assert response.status_code == 404

    @patch('app.AI_Controller.get_analysis_run_list')
    def test_get_analysis_run_list_success(self, mock_get_list):
        """Test successful retrieval of analysis run list."""
        # Mock the get_analysis_run_list function
        mock_get_list.return_value = {
            "status": "OK",
            "ids": ["analysis-1", "analysis-2", "analysis-3"]
        }

        response = self.client.get("/analysis/list")
        assert response.status_code == 200
        mock_get_list.assert_called_once()

    def test_cancel_queued_analysis(self):
        """Test cancelling queued analysis."""
        request_data = {
            "analysis_ids": ["analysis-1", "analysis-2"]
        }

        response = self.client.post("/analysis/cancel-queued/", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert "Cancelled queued analyses" in data["message"]


class TestSECEndpoints:
    """Test class for SEC-related endpoints."""

    def setup_method(self):
        """Set up test client and mocks before each test."""
        self.client = TestClient(app)

    @patch('app.app.sec')
    def test_get_sec_submissions_success(self, mock_sec):
        """Test successful retrieval of SEC submissions."""
        # Mock the SEC controller
        mock_sec.symbol_store.get_symbol_cik.return_value = 320193
        mock_sec.get_company_submissions.return_value = {
            "cik": 320193,
            "entityType": "operating",
            "sic": "3571",
            "sicDescription": "Electronic Computers"
        }

        response = self.client.get("/sec/submissions/AAPL")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC submissions OK"

    @patch('app.app.sec')
    def test_get_sec_submissions_not_found(self, mock_sec):
        """Test SEC submissions not found."""
        # Mock the SEC controller to raise an exception
        mock_sec.symbol_store.get_symbol_cik.side_effect = Exception("Symbol not found")

        response = self.client.get("/sec/submissions/INVALID")
        assert response.status_code == 404

    @patch('app.app.sec')
    def test_get_sec_filings_success(self, mock_sec):
        """Test successful retrieval of SEC filings."""
        # Mock the SEC controller
        mock_sec.symbol_store.get_symbol_cik.return_value = 320193
        mock_sec.get_company_filings.return_value = [
            {
                "accession_number": "0000320193-23-000064",
                "cik": 320193,
                "ticker": "AAPL",
                "form": "10-K",
                "filing_date": "2023-11-03"
            }
        ]

        response = self.client.get("/sec/filings/AAPL")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC filings OK"

    @patch('app.app.sec')
    def test_get_sec_symbols_success(self, mock_sec):
        """Test successful retrieval of SEC symbols."""
        # Mock the SEC controller
        mock_sec.get_company_tickers.return_value = {
            "320193": {"ticker": "AAPL", "title": "Apple Inc."},
            "789019": {"ticker": "MSFT", "title": "Microsoft Corporation"}
        }

        response = self.client.get("/sec/symbols")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC symbols OK"

    @patch('app.app.sec')
    def test_get_sec_facts_success(self, mock_sec):
        """Test successful retrieval of SEC facts."""
        # Mock the SEC controller
        mock_sec.symbol_store.get_symbol_cik.return_value = 320193
        mock_sec.get_company_facts.return_value = {
            "cik": 320193,
            "entityName": "Apple Inc.",
            "facts": {
                "us-gaap": {
                    "Assets": {
                        "label": "Assets",
                        "description": "Sum of the carrying amounts..."
                    }
                }
            }
        }

        response = self.client.get("/sec/facts/AAPL")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC Facts OK"

    @patch('app.app.sec')
    def test_convert_symbol_success(self, mock_sec):
        """Test successful symbol conversion."""
        # Mock the SEC controller
        mock_sec.symbol_store.get_symbol_cik.return_value = 320193
        mock_sec.symbol_store.get_symbol_ticker.return_value = "AAPL"
        mock_sec.symbol_store.get_symbol_name.return_value = "Apple Inc."

        response = self.client.get("/sec/convert/AAPL")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC Convert Symbol OK"
        assert data["data"]["cik"] == 320193
        assert data["data"]["ticker"] == "AAPL"
        assert data["data"]["name"] == "Apple Inc."


class TestDocumentEndpoints:
    """Test class for document retrieval endpoints."""

    def setup_method(self):
        """Set up test client and mocks before each test."""
        self.client = TestClient(app)

    @patch('app.app.sec')
    def test_get_sec_10k_success(self, mock_sec):
        """Test successful retrieval of 10-K document."""
        # Mock the SEC controller
        mock_sec.get_10k.return_value = "Sample 10-K document content"

        response = self.client.get("/sec/10k/AAPL/2023")
        assert response.status_code == 200
        data = response.json()
        assert data["content"] == "Sample 10-K document content"
        assert data["document_ref"]["type"] == "10-k"

    @patch('app.app.sec')
    def test_get_sec_8k_list_success(self, mock_sec):
        """Test successful retrieval of 8-K list."""
        # Mock the SEC controller
        mock_sec.get_8k_list.return_value = [
            {
                "accession_number": "0000320193-23-000001",
                "form": "8-K",
                "filing_date": "2023-01-15"
            }
        ]

        response = self.client.get("/sec/8k/list/AAPL")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC 8-K OK"

    @patch('app.app.sec')
    def test_get_sec_10q_success(self, mock_sec):
        """Test successful retrieval of 10-Q document."""
        # Mock the SEC controller
        mock_sec.get_10q.return_value = "Sample 10-Q document content"

        response = self.client.get("/sec/10q/AAPL/2023/1")
        assert response.status_code == 200
        data = response.json()
        assert data["content"] == "Sample 10-Q document content"
        assert data["document_ref"]["type"] == "10-q"

    @patch('app.app.sec')
    def test_get_sec_exhibit_success(self, mock_sec):
        """Test successful retrieval of SEC exhibit."""
        # Mock the SEC controller
        mock_sec.get_exhibit.return_value = "Sample exhibit content"

        response = self.client.get("/sec/exhibit/AAPL/0000320193-23-000064/EX-21.1")
        assert response.status_code == 200
        data = response.json()
        assert data["content"] == "Sample exhibit content"
        assert data["document_ref"]["type"] == "exhibit"

    @patch('app.app.sec')
    def test_get_sec_forms_success(self, mock_sec):
        """Test successful retrieval of specific form types."""
        # Mock the SEC controller
        mock_sec.get_form_list.return_value = [
            {
                "accession_number": "0000320193-23-000064",
                "form": "10-K",
                "filing_date": "2023-11-03"
            }
        ]

        response = self.client.get("/sec/forms/10-K/AAPL")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC Forms OK"

    @patch('app.app.sec')
    def test_get_daily_filings_success(self, mock_sec):
        """Test successful retrieval of daily filings."""
        # Mock the SEC controller
        mock_sec.get_daily_filing.return_value = [
            {
                "accession_number": "0000320193-23-000064",
                "cik": 320193,
                "form": "10-K",
                "filing_date": "2023-11-03"
            }
        ]

        response = self.client.get("/sec/daily-filings?date=20231103&form_type=10-K")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "SEC Daily Filings OK"


class TestYahooFinanceEndpoints:
    """Test class for Yahoo Finance endpoints."""

    def setup_method(self):
        """Set up test client and mocks before each test."""
        self.client = TestClient(app)

    @patch('app.app.yf')
    @patch('app.app.sec')
    def test_get_price_history_success(self, mock_sec, mock_yf):
        """Test successful retrieval of price history."""
        # Mock the SEC controller and yfinance
        mock_sec.symbol_store.get_symbol_ticker.return_value = "AAPL"

        # Create a mock DataFrame-like object
        mock_price_data = MagicMock()
        mock_price_data.to_dict.return_value = {
            "Open": {"2023-01-01": 150.0},
            "High": {"2023-01-01": 155.0},
            "Low": {"2023-01-01": 149.0},
            "Close": {"2023-01-01": 154.0},
            "Volume": {"2023-01-01": 1000000}
        }
        mock_yf.download.return_value = mock_price_data

        response = self.client.get("/yahoo/prices/AAPL?period=1y")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "Yahoo Prices OK"


def main():
    """Main function to run the tests."""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    main()
