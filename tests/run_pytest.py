"""
Enhanced pytest-based test runner for the AI Analyst application
"""
import os
import subprocess
import sys

# Add the parent directory to the path so we can import app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def run_pytest(test_pattern=None, verbose=True, coverage=False):
    """Run tests using pytest with optional filtering and coverage"""
    print("Running tests with pytest...")

    # Get the tests directory
    tests_dir = os.path.dirname(__file__)

    # Build pytest command
    cmd = [sys.executable, "-m", "pytest"]

    # Add test directory or specific pattern
    if test_pattern:
        cmd.append(os.path.join(tests_dir, test_pattern))
    else:
        cmd.append(tests_dir)

    # Add options
    if verbose:
        cmd.extend(["-v", "--tb=short"])

    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])

    cmd.extend(["--color=yes", "--durations=10"])

    try:
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running pytest: {e}")
        return False

def run_specific_tests():
    """Run specific test categories"""
    test_categories = {
        "1": ("test_ai_controller.py", "AI Controller Unit Tests"),
        "2": ("test_ai_controller_integration.py", "AI Controller Integration Tests"),
        "3": ("test_app.py", "FastAPI Endpoint Tests"),
        "4": ("test_simple.py", "Basic Functionality Tests"),
        "5": ("test_*ai*.py", "All AI Controller Tests"),
        "6": ("", "All Tests")
    }

    print("\nAvailable test categories:")
    for key, (pattern, description) in test_categories.items():
        print(f"{key}. {description}")

    choice = input("\nSelect test category (1-6): ").strip()

    if choice in test_categories:
        pattern, description = test_categories[choice]
        print(f"\nRunning {description}...")
        return run_pytest(pattern if pattern else None)
    else:
        print("Invalid choice. Running all tests...")
        return run_pytest()

def main():
    """Main function to run tests"""
    print("AI Analyst Enhanced Pytest Test Runner")
    print("=" * 50)

    # Check if pytest is available
    try:
        subprocess.run([sys.executable, "-m", "pytest", "--version"],
                      capture_output=True, check=True)
    except subprocess.CalledProcessError:
        print("❌ pytest is not installed. Please install it with: pip install pytest")
        return

    # Ask user for test options
    print("\nTest Options:")
    print("1. Run all tests")
    print("2. Run specific test category")
    print("3. Run with coverage report")

    choice = input("\nSelect option (1-3): ").strip()

    success = False
    if choice == "1":
        success = run_pytest()
    elif choice == "2":
        success = run_specific_tests()
    elif choice == "3":
        success = run_pytest(coverage=True)
    else:
        print("Invalid choice. Running all tests...")
        success = run_pytest()

    # Print results
    if success:
        print("\n✅ All selected tests passed!")
        print("\nTest Summary:")
        print("- AI Controller unit tests: Comprehensive coverage of all functions")
        print("- AI Controller integration tests: End-to-end workflow testing")
        print("- FastAPI endpoint tests: API contract validation")
        print("- Basic functionality tests: Core system verification")
    else:
        print("\n❌ Some tests failed")
        print("\nTroubleshooting:")
        print("- Check that all dependencies are installed")
        print("- Verify that the app module can be imported")
        print("- Review test output for specific error details")

if __name__ == "__main__":
    main()
